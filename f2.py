clear()
def tillCorrectly():
        for i in range(get_world_size()):
            if get_ground_type == "dirt":
                    till()
                    move(North)
            
def plantCarrots():
        for i in range(get_world_size()):
            plant(Entities.Carrot)
            move(North)


def plantBushes():
    for i in range(get_world_size()):
            plant(Entities.Bush)
            move(North)

def plantGress():
       for i in range(get_world_size()):
            plant(Entities.Grass)
            move(North)

while True:
       for i in range(get_world_size()):
              tillCorrectly()
              plantCarrots()
              move(East)
       for i in range(get_world_size()):
             plantBushes()
             move(East)
       for i in range(get_world_size()):
             plantGress()
             move(East)